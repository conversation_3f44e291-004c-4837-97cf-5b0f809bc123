/**
 * 统一工具函数导出
 * 提供基于新架构统一类型的工具函数
 */

// ==================== 媒体类型检测工具 ====================
export {
  // 类型定义
  type DetectedMediaType,

  // 配置常量
  SUPPORTED_MEDIA_TYPES,
  FILE_SIZE_LIMITS,

  // 检测函数
  detectFileMediaType,
  isSupportedMediaType,
  isSupportedMimeType,
  getMediaTypeFromMimeType,
  getMediaTypeDisplayName,
  getMediaTypeIcon
} from './mediaTypeDetector'

// ==================== 统一时间范围工具 ====================
export {
  // 同步工具
  syncTimeRange,
  
  // 验证工具
  validateBaseTimeRange,
  validateTimelineItemTimeRange,
  
  // 计算工具
  calculateDuration,
  containsFrame,
  isTimeRangeOverlapping,
  calculateOverlapDuration,
  
  // 操作工具
  moveTimelineItem,
  resizeTimelineItem,
  trimTimelineItem,
  
  // 工具集合
  UnifiedTimeRangeUtils
} from './UnifiedTimeRangeUtils'

// ==================== 统一WebAV动画管理器 ====================
export {
  // 管理器类
  UnifiedWebAVAnimationManager,

  // 全局管理器
  globalUnifiedWebAVAnimationManager
} from './UnifiedWebAVAnimationManager'

// ==================== 统一Composables ====================
export {
  // 统一播放控制
  useUnifiedPlaybackControls,
  usePlaybackControls // 兼容性导出
} from './useUnifiedPlaybackControls'

export {
  // 统一拖拽工具
  useUnifiedDragUtils,
  useDragUtils // 兼容性导出
} from './useUnifiedDragUtils'

export {
  // 统一拖拽预览管理器
  getUnifiedDragPreviewManager,
  resetUnifiedDragPreviewManager,
  UnifiedDragPreviewManager,

  // 兼容性导出
  getDragPreviewManager,

  // 类型导出
  type DragPreviewData
} from './useUnifiedDragPreview'

// ==================== 统一Sprite工厂 ====================
export {
  // 主要工厂函数
  createSpriteFromUnifiedMediaItem,
  createSpritesFromUnifiedMediaItems,

  // 检查和状态函数
  canCreateSpriteFromUnifiedMediaItem,
  getSpriteCreationStatus,

  // 兼容性函数
  createSpriteFromMediaItem,

  // 默认导出
  default as unifiedSpriteFactory
} from './unifiedSpriteFactory'

// ==================== 统一对话框管理 ====================
export {
  // 统一对话框工具
  useUnifiedDialogs,
  useDialogs // 兼容性导出
} from './useUnifiedDialogs'

// ==================== 统一键盘快捷键 ====================
export {
  // 统一键盘快捷键
  useUnifiedKeyboardShortcuts,
  useKeyboardShortcuts // 兼容性导出
} from './useUnifiedKeyboardShortcuts'

// ==================== 统一自动保存 ====================
export {
  // 统一自动保存
  useUnifiedAutoSave,
  useAutoSave // 兼容性导出
} from './useUnifiedAutoSave'

// ==================== 统一吸附管理 ====================
export {
  // 统一吸附管理器
  useUnifiedSnapManager,
  useSnapManager // 兼容性导出
} from './useUnifiedSnapManager'

export {
  // 统一吸附指示器管理器
  getUnifiedSnapIndicatorManager,
  resetUnifiedSnapIndicatorManager,
  useUnifiedSnapIndicator,
  UnifiedSnapIndicatorManager,

  // 兼容性导出
  useSnapIndicator,
  getSnapIndicatorManager
} from './useUnifiedSnapIndicator'
