/**
 * 统一Sprite工厂函数
 * 基于新架构的统一类型系统重构的Sprite创建逻辑
 * 
 * 架构特点：
 * 1. 使用 UnifiedMediaItemData 替代 LocalMediaItem
 * 2. 适配新的统一状态管理系统
 * 3. 增强的错误处理和状态检查
 * 4. 支持新架构的WebAV集成方式
 * 5. 保持与现有Sprite类的兼容性
 */

import type { Raw } from 'vue'
import type { UnifiedMediaItemData, MediaType } from '../index'
import { VideoVisibleSprite } from '../../utils/VideoVisibleSprite'
import { ImageVisibleSprite } from '../../utils/ImageVisibleSprite'
import { AudioVisibleSprite } from '../../utils/AudioVisibleSprite'

/**
 * 从统一媒体项目创建对应的 Sprite 实例
 * 统一的工厂函数，避免在多个地方重复相同的创建逻辑
 *
 * @param mediaItem 统一媒体项目数据
 * @returns 创建的 Sprite 实例
 * @throws 当媒体项目未准备好或类型不支持时抛出错误
 */
export async function createSpriteFromUnifiedMediaItem(
  mediaItem: UnifiedMediaItemData,
): Promise<VideoVisibleSprite | ImageVisibleSprite | AudioVisibleSprite> {
  console.log('🏭 [UnifiedSpriteFactory] 开始创建Sprite:', {
    id: mediaItem.id,
    name: mediaItem.name,
    mediaType: mediaItem.mediaType,
    mediaStatus: mediaItem.mediaStatus
  })

  // 检查媒体项目状态是否已准备好
  if (mediaItem.mediaStatus !== 'ready') {
    const error = `统一媒体项目尚未解析完成: ${mediaItem.name} (状态: ${mediaItem.mediaStatus})`
    console.error('❌ [UnifiedSpriteFactory]', error)
    throw new Error(error)
  }

  // 检查是否有WebAV对象
  if (!mediaItem.webav) {
    const error = `统一媒体项目缺少WebAV对象: ${mediaItem.name}`
    console.error('❌ [UnifiedSpriteFactory]', error)
    throw new Error(error)
  }

  // 动态导入统一Store以避免循环依赖
  const { useUnifiedStore } = await import('../unifiedStore')
  const unifiedStore = useUnifiedStore()

  try {
    let sprite: VideoVisibleSprite | ImageVisibleSprite | AudioVisibleSprite

    switch (mediaItem.mediaType) {
      case 'video':
        sprite = await createVideoSprite(mediaItem, unifiedStore)
        break
      
      case 'image':
        sprite = await createImageSprite(mediaItem, unifiedStore)
        break
      
      case 'audio':
        sprite = await createAudioSprite(mediaItem, unifiedStore)
        break
      
      default:
        const error = `不支持的媒体类型: ${mediaItem.mediaType}`
        console.error('❌ [UnifiedSpriteFactory]', error)
        throw new Error(error)
    }

    console.log('✅ [UnifiedSpriteFactory] Sprite创建成功:', {
      id: mediaItem.id,
      mediaType: mediaItem.mediaType,
      spriteType: sprite.constructor.name
    })

    return sprite

  } catch (error) {
    console.error('❌ [UnifiedSpriteFactory] Sprite创建失败:', {
      id: mediaItem.id,
      mediaType: mediaItem.mediaType,
      error: error instanceof Error ? error.message : String(error)
    })
    throw error
  }
}

/**
 * 创建视频Sprite
 */
async function createVideoSprite(
  mediaItem: UnifiedMediaItemData,
  unifiedStore: any
): Promise<VideoVisibleSprite> {
  if (!mediaItem.webav?.mp4Clip) {
    throw new Error(`视频素材解析失败，无法创建sprite: ${mediaItem.name}`)
  }

  console.log('🎬 [UnifiedSpriteFactory] 创建视频Sprite:', mediaItem.name)
  
  // 使用统一Store的克隆方法
  const clonedMP4Clip = await unifiedStore.cloneMP4Clip(mediaItem.webav.mp4Clip)
  return new VideoVisibleSprite(clonedMP4Clip)
}

/**
 * 创建图片Sprite
 */
async function createImageSprite(
  mediaItem: UnifiedMediaItemData,
  unifiedStore: any
): Promise<ImageVisibleSprite> {
  if (!mediaItem.webav?.imgClip) {
    throw new Error(`图片素材解析失败，无法创建sprite: ${mediaItem.name}`)
  }

  console.log('🖼️ [UnifiedSpriteFactory] 创建图片Sprite:', mediaItem.name)
  
  // 使用统一Store的克隆方法
  const clonedImgClip = await unifiedStore.cloneImgClip(mediaItem.webav.imgClip)
  return new ImageVisibleSprite(clonedImgClip)
}

/**
 * 创建音频Sprite
 */
async function createAudioSprite(
  mediaItem: UnifiedMediaItemData,
  unifiedStore: any
): Promise<AudioVisibleSprite> {
  if (!mediaItem.webav?.audioClip) {
    throw new Error(`音频素材解析失败，无法创建sprite: ${mediaItem.name}`)
  }

  console.log('🎵 [UnifiedSpriteFactory] 创建音频Sprite:', mediaItem.name)
  
  // 使用统一Store的克隆方法
  const clonedAudioClip = await unifiedStore.cloneAudioClip(mediaItem.webav.audioClip)
  return new AudioVisibleSprite(clonedAudioClip)
}

/**
 * 批量创建Sprite（用于批量操作场景）
 */
export async function createSpritesFromUnifiedMediaItems(
  mediaItems: UnifiedMediaItemData[]
): Promise<Array<VideoVisibleSprite | ImageVisibleSprite | AudioVisibleSprite>> {
  console.log('🏭 [UnifiedSpriteFactory] 批量创建Sprite:', mediaItems.length)

  const sprites: Array<VideoVisibleSprite | ImageVisibleSprite | AudioVisibleSprite> = []
  const errors: Array<{ mediaItem: UnifiedMediaItemData; error: Error }> = []

  for (const mediaItem of mediaItems) {
    try {
      const sprite = await createSpriteFromUnifiedMediaItem(mediaItem)
      sprites.push(sprite)
    } catch (error) {
      console.error('❌ [UnifiedSpriteFactory] 批量创建中的错误:', {
        mediaItem: mediaItem.name,
        error: error instanceof Error ? error.message : String(error)
      })
      errors.push({ 
        mediaItem, 
        error: error instanceof Error ? error : new Error(String(error))
      })
    }
  }

  if (errors.length > 0) {
    console.warn('⚠️ [UnifiedSpriteFactory] 批量创建完成，但有错误:', {
      成功: sprites.length,
      失败: errors.length,
      错误详情: errors
    })
  } else {
    console.log('✅ [UnifiedSpriteFactory] 批量创建全部成功:', sprites.length)
  }

  return sprites
}

/**
 * 检查媒体项目是否可以创建Sprite
 */
export function canCreateSpriteFromUnifiedMediaItem(mediaItem: UnifiedMediaItemData): boolean {
  // 检查状态
  if (mediaItem.mediaStatus !== 'ready') {
    return false
  }

  // 检查是否有WebAV对象
  if (!mediaItem.webav) {
    return false
  }

  // 检查具体的WebAV对象
  switch (mediaItem.mediaType) {
    case 'video':
      return !!mediaItem.webav.mp4Clip
    case 'image':
      return !!mediaItem.webav.imgClip
    case 'audio':
      return !!mediaItem.webav.audioClip
    default:
      return false
  }
}

/**
 * 获取Sprite创建状态信息（用于调试）
 */
export function getSpriteCreationStatus(mediaItem: UnifiedMediaItemData) {
  return {
    canCreate: canCreateSpriteFromUnifiedMediaItem(mediaItem),
    mediaStatus: mediaItem.mediaStatus,
    mediaType: mediaItem.mediaType,
    hasWebAV: !!mediaItem.webav,
    webavDetails: mediaItem.webav ? {
      hasMP4Clip: !!mediaItem.webav.mp4Clip,
      hasImgClip: !!mediaItem.webav.imgClip,
      hasAudioClip: !!mediaItem.webav.audioClip,
    } : null,
  }
}

/**
 * 兼容性函数 - 从旧的LocalMediaItem创建Sprite
 * 这个函数提供向后兼容，但建议迁移到新的统一类型
 */
export async function createSpriteFromMediaItem(
  mediaItem: any // 旧的LocalMediaItem类型
): Promise<VideoVisibleSprite | ImageVisibleSprite | AudioVisibleSprite> {
  console.warn('⚠️ [UnifiedSpriteFactory] 使用了兼容性函数，建议迁移到统一类型')
  
  // 这里可以添加从旧类型到新类型的转换逻辑
  // 或者直接调用旧的工厂函数
  const { createSpriteFromMediaItem: oldFactory } = await import('../../utils/spriteFactory')
  return oldFactory(mediaItem)
}

/**
 * 默认导出 - 主要的工厂函数
 */
export default createSpriteFromUnifiedMediaItem
