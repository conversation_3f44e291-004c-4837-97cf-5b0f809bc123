/**
 * 统一自动保存功能
 * 基于新架构的统一类型系统重构的自动保存功能
 * 
 * 架构特点：
 * 1. 使用 useUnifiedStore 替代旧的 videoStore
 * 2. 适配新的统一类型系统
 * 3. 监听新架构的状态变化
 * 4. 增强的错误处理和重试机制
 * 5. 保持与旧版本兼容的接口
 */

import { ref, watch, onUnmounted } from 'vue'
import { useUnifiedStore } from '../unifiedStore'
import type { AutoSaveConfig, AutoSaveState } from '../../types'

/**
 * 统一自动保存 Composable
 * 提供防抖+节流的自动保存策略
 */
export function useUnifiedAutoSave(config: Partial<AutoSaveConfig> = {}) {
  const unifiedStore = useUnifiedStore()

  // 默认配置
  const defaultConfig: AutoSaveConfig = {
    debounceTime: 2000, // 2秒防抖
    throttleTime: 30000, // 30秒强制保存
    maxRetries: 3,
    enabled: true
  }

  const finalConfig = { ...defaultConfig, ...config }

  // 自动保存状态
  const autoSaveState = ref<AutoSaveState>({
    isEnabled: finalConfig.enabled,
    lastSaveTime: null,
    saveCount: 0,
    errorCount: 0,
    isDirty: false
  })

  // 定时器引用
  let debounceTimer: number | null = null
  let throttleTimer: number | null = null
  let retryCount = 0

  /**
   * 清除所有定时器
   */
  function clearTimers() {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }
    if (throttleTimer) {
      clearTimeout(throttleTimer)
      throttleTimer = null
    }
  }

  /**
   * 执行保存操作（适配新架构）
   */
  async function performSave(): Promise<boolean> {
    if (!unifiedStore.hasCurrentProject) {
      console.log('🔄 [UnifiedAutoSave] 没有当前项目，跳过自动保存')
      return false
    }

    if (unifiedStore.isSaving) {
      console.log('🔄 [UnifiedAutoSave] 正在保存中，跳过此次自动保存')
      return false
    }

    try {
      console.log('💾 [UnifiedAutoSave] 开始自动保存...')

      // 构建项目数据（适配新架构）
      const projectData = {
        id: unifiedStore.currentProjectId,
        name: unifiedStore.currentProjectName || '未命名项目',
        description: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0',
        
        // 时间轴数据（使用新架构的统一类型）
        timeline: {
          tracks: unifiedStore.tracks.map(track => ({
            id: track.id,
            name: track.name,
            type: track.type,
            isVisible: track.visibility.isVisible,
            isMuted: track.audio.isMuted,
            // 其他轨道属性...
          })),
          timelineItems: unifiedStore.timelineItems.map(item => ({
            id: item.id,
            mediaItemId: item.mediaItemId,
            trackId: item.trackId,
            mediaType: item.mediaType,
            timeRange: item.timeRange,
            config: item.config,
            // 注意：不保存运行时状态
          })),
          mediaItems: unifiedStore.mediaItems.map(item => ({
            id: item.id,
            name: item.name,
            mediaType: item.mediaType,
            duration: item.duration,
            // 注意：不保存运行时状态和WebAV对象
          }))
        },
        
        // 项目设置
        settings: {
          videoResolution: unifiedStore.videoResolution,
          frameRate: unifiedStore.frameRate,
          timelineDurationFrames: unifiedStore.timelineDurationFrames
        },
        
        // 导出历史
        exports: []
      }

      // 使用统一Store的保存方法
      await unifiedStore.saveCurrentProject(projectData)
      
      // 更新状态
      autoSaveState.value.lastSaveTime = new Date()
      autoSaveState.value.saveCount++
      autoSaveState.value.isDirty = false
      retryCount = 0

      console.log('✅ [UnifiedAutoSave] 自动保存成功')
      return true
      
    } catch (error) {
      console.error('❌ [UnifiedAutoSave] 自动保存失败:', error)
      autoSaveState.value.errorCount++
      
      // 重试机制
      if (retryCount < finalConfig.maxRetries) {
        retryCount++
        console.log(`🔄 [UnifiedAutoSave] 准备重试 (${retryCount}/${finalConfig.maxRetries})`)
        
        // 延迟重试
        setTimeout(() => {
          performSave()
        }, 5000 * retryCount) // 递增延迟
      } else {
        console.error('❌ [UnifiedAutoSave] 达到最大重试次数，停止自动保存')
        retryCount = 0
      }
      
      return false
    }
  }

  /**
   * 触发自动保存（防抖+节流）
   */
  function triggerAutoSave() {
    if (!autoSaveState.value.isEnabled) {
      return
    }

    // 标记为有未保存的更改
    autoSaveState.value.isDirty = true

    // 清除之前的防抖定时器
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }

    // 设置防抖定时器
    debounceTimer = setTimeout(() => {
      performSave()
    }, finalConfig.debounceTime)

    // 如果没有节流定时器，设置一个
    if (!throttleTimer) {
      throttleTimer = setTimeout(() => {
        // 强制保存（节流）
        if (autoSaveState.value.isDirty) {
          console.log('⏰ [UnifiedAutoSave] 节流触发强制保存')
          performSave()
        }
        throttleTimer = null
      }, finalConfig.throttleTime)
    }
  }

  /**
   * 启用自动保存
   */
  function enableAutoSave() {
    autoSaveState.value.isEnabled = true
    console.log('✅ [UnifiedAutoSave] 自动保存已启用')
  }

  /**
   * 禁用自动保存
   */
  function disableAutoSave() {
    autoSaveState.value.isEnabled = false
    clearTimers()
    console.log('⏸️ [UnifiedAutoSave] 自动保存已禁用')
  }

  /**
   * 手动触发保存
   */
  async function manualSave(): Promise<boolean> {
    clearTimers() // 清除自动保存定时器
    return await performSave()
  }

  /**
   * 重置自动保存状态
   */
  function resetAutoSaveState() {
    autoSaveState.value = {
      isEnabled: finalConfig.enabled,
      lastSaveTime: null,
      saveCount: 0,
      errorCount: 0,
      isDirty: false
    }
    retryCount = 0
    clearTimers()
  }

  /**
   * 获取自动保存状态摘要（用于调试）
   */
  function getAutoSaveSummary() {
    return {
      ...autoSaveState.value,
      config: finalConfig,
      retryCount,
      hasTimers: {
        debounce: !!debounceTimer,
        throttle: !!throttleTimer,
      },
      projectInfo: {
        hasCurrentProject: unifiedStore.hasCurrentProject,
        isSaving: unifiedStore.isSaving,
        timelineItemsCount: unifiedStore.timelineItems.length,
        mediaItemsCount: unifiedStore.mediaItems.length,
        tracksCount: unifiedStore.tracks.length,
      }
    }
  }

  // 监听关键状态变化（适配新架构）
  if (finalConfig.enabled) {
    // 监听时间轴项目变化
    watch(
      () => unifiedStore.timelineItems,
      () => {
        console.log('🔄 [UnifiedAutoSave] 检测到时间轴项目变化')
        triggerAutoSave()
      },
      { deep: true }
    )

    // 监听轨道变化
    watch(
      () => unifiedStore.tracks,
      () => {
        console.log('🔄 [UnifiedAutoSave] 检测到轨道变化')
        triggerAutoSave()
      },
      { deep: true }
    )

    // 监听媒体项目变化
    watch(
      () => unifiedStore.mediaItems,
      () => {
        console.log('🔄 [UnifiedAutoSave] 检测到媒体项目变化')
        triggerAutoSave()
      },
      { deep: true }
    )

    // 监听项目配置变化
    watch(
      () => ({
        videoResolution: unifiedStore.videoResolution,
        frameRate: unifiedStore.frameRate,
        timelineDurationFrames: unifiedStore.timelineDurationFrames
      }),
      () => {
        console.log('🔄 [UnifiedAutoSave] 检测到项目配置变化')
        triggerAutoSave()
      },
      { deep: true }
    )
  }

  // 清理函数
  onUnmounted(() => {
    clearTimers()
  })

  return {
    // 状态
    autoSaveState,
    
    // 控制方法
    enableAutoSave,
    disableAutoSave,
    triggerAutoSave,
    manualSave,
    resetAutoSaveState,
    
    // 调试方法
    getAutoSaveSummary,
  }
}

/**
 * 兼容性导出 - 保持与旧版本的接口兼容
 */
export const useAutoSave = useUnifiedAutoSave
