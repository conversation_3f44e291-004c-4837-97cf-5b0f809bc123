/**
 * 统一对话框工具
 * 基于新架构的统一类型系统重构的对话框管理功能
 * 
 * 架构特点：
 * 1. 使用 useUnifiedStore 替代旧的 videoStore
 * 2. 适配新的统一通知系统
 * 3. 保持与旧版本兼容的接口
 * 4. 增强的类型安全和错误处理
 * 5. 支持新架构的状态管理
 */

import { useUnifiedStore } from '../unifiedStore'

/**
 * 统一对话框工具函数
 * 替换原生的 alert() 和 confirm() 使用统一的通知系统
 */
export function useUnifiedDialogs() {
  const unifiedStore = useUnifiedStore()

  /**
   * 显示信息提示（替换 alert）
   * @param title 标题
   * @param message 消息内容
   * @param duration 显示时长（毫秒）
   */
  function showInfo(title: string, message?: string, duration?: number): void {
    unifiedStore.showInfo(title, message, duration)
    console.log('ℹ️ [UnifiedDialogs] 显示信息:', { title, message })
  }

  /**
   * 显示错误提示（替换 alert 的错误场景）
   * @param title 标题
   * @param message 消息内容
   * @param duration 显示时长（毫秒）
   */
  function showError(title: string, message?: string, duration?: number): void {
    unifiedStore.showError(title, message, duration)
    console.error('❌ [UnifiedDialogs] 显示错误:', { title, message })
  }

  /**
   * 显示警告提示
   * @param title 标题
   * @param message 消息内容
   * @param duration 显示时长（毫秒）
   */
  function showWarning(title: string, message?: string, duration?: number): void {
    unifiedStore.showWarning(title, message, duration)
    console.warn('⚠️ [UnifiedDialogs] 显示警告:', { title, message })
  }

  /**
   * 显示成功提示
   * @param title 标题
   * @param message 消息内容
   * @param duration 显示时长（毫秒）
   */
  function showSuccess(title: string, message?: string, duration?: number): void {
    unifiedStore.showSuccess(title, message, duration)
    console.log('✅ [UnifiedDialogs] 显示成功:', { title, message })
  }

  /**
   * 显示确认对话框（替换 confirm）
   * @param title 标题
   * @param message 消息内容
   * @returns 用户是否确认
   */
  function confirm(title: string, message?: string): boolean {
    const result = window.confirm(message ? `${title}\n\n${message}` : title)
    console.log('❓ [UnifiedDialogs] 确认对话框:', { title, message, result })
    return result
  }

  /**
   * 文件类型验证提示
   * @param acceptedTypes 接受的文件类型描述
   */
  function showFileTypeError(acceptedTypes: string = '视频或图片文件'): void {
    showError('文件类型错误', `请选择${acceptedTypes}`)
  }

  /**
   * 操作失败提示
   * @param operation 操作名称
   * @param error 错误信息
   */
  function showOperationError(operation: string, error?: string): void {
    const message = error ? `${operation}失败：${error}` : `${operation}失败`
    showError('操作失败', message)
  }

  /**
   * 删除确认对话框
   * @param itemName 要删除的项目名称
   * @param itemType 项目类型（如：素材、轨道等）
   * @param additionalInfo 额外信息
   * @returns 用户是否确认删除
   */
  function confirmDelete(
    itemName: string,
    itemType: string = '项目',
    additionalInfo?: string,
  ): boolean {
    let message = `确定要删除${itemType} "${itemName}" 吗？`
    if (additionalInfo) {
      message += `\n\n${additionalInfo}`
    }
    return confirm('确认删除', message)
  }

  /**
   * 轨道删除确认（特殊场景）
   * @param trackId 轨道ID
   * @param relatedItemsCount 相关项目数量
   * @returns 用户是否确认删除
   */
  function confirmTrackDelete(trackId: string, relatedItemsCount: number = 0): boolean {
    let message = `确定要删除轨道 ${trackId} 吗？`
    if (relatedItemsCount > 0) {
      message += `\n\n注意：这将同时删除轨道上的 ${relatedItemsCount} 个片段。`
    }
    return confirm('确认删除轨道', message)
  }

  /**
   * 素材删除确认（特殊场景）
   * @param mediaName 素材名称
   * @param relatedTimelineItemsCount 相关时间轴项目数量
   * @returns 用户是否确认删除
   */
  function confirmMediaDelete(mediaName: string, relatedTimelineItemsCount: number = 0): boolean {
    let message = `确定要删除素材 "${mediaName}" 吗？\n\n⚠️ 这将永久删除本地文件，无法恢复！`
    if (relatedTimelineItemsCount > 0) {
      message += `\n\n同时还会删除时间轴上的 ${relatedTimelineItemsCount} 个相关片段。`
    }
    return confirm('确认删除素材', message)
  }

  /**
   * 项目保存确认对话框
   * @param hasUnsavedChanges 是否有未保存的更改
   * @returns 用户选择（save/discard/cancel）
   */
  function confirmProjectSave(hasUnsavedChanges: boolean): 'save' | 'discard' | 'cancel' {
    if (!hasUnsavedChanges) {
      return 'discard' // 没有未保存的更改，直接继续
    }

    const message = '当前项目有未保存的更改，是否保存？\n\n点击"确定"保存，点击"取消"放弃更改。'
    const result = confirm('保存项目', message)
    
    return result ? 'save' : 'discard'
  }

  /**
   * 批量操作确认对话框
   * @param operation 操作名称
   * @param itemCount 项目数量
   * @param itemType 项目类型
   * @returns 用户是否确认
   */
  function confirmBatchOperation(
    operation: string,
    itemCount: number,
    itemType: string = '项目'
  ): boolean {
    const message = `确定要${operation} ${itemCount} 个${itemType}吗？`
    return confirm(`批量${operation}`, message)
  }

  /**
   * 最少轨道数量限制提示
   */
  function showMinTrackWarning(): void {
    showWarning('无法删除', '至少需要保留一个轨道')
  }

  /**
   * 拖拽数据格式错误提示
   */
  function showDragDataError(): void {
    showError('拖拽失败', '拖拽数据格式错误')
  }

  /**
   * 无效拖拽提示
   */
  function showInvalidDragWarning(): void {
    showInfo('拖拽提示', '请先将视频或图片文件导入到素材库，然后从素材库拖拽到时间轴')
  }

  /**
   * WebAV未就绪提示
   */
  function showWebAVNotReadyWarning(): void {
    showWarning('操作失败', 'WebAV引擎尚未就绪，请稍后再试')
  }

  /**
   * 媒体项目未就绪提示
   */
  function showMediaNotReadyWarning(mediaName: string): void {
    showWarning('操作失败', `素材"${mediaName}"尚未加载完成，请稍后再试`)
  }

  // 注意：为了保持与旧版本的逻辑一致性，暂时不添加导出相关方法
  // 这些功能在旧版本中不存在，应该在后续版本中逐步添加

  /**
   * 获取对话框状态摘要（用于调试）
   */
  function getDialogsSummary() {
    return {
      notificationCount: unifiedStore.notifications?.length || 0,
      hasActiveNotifications: (unifiedStore.notifications?.length || 0) > 0,
      storeReady: !!unifiedStore,
    }
  }

  return {
    // 基础提示方法
    showInfo,
    showError,
    showWarning,
    showSuccess,
    confirm,

    // 专用提示方法
    showFileTypeError,
    showOperationError,
    showMinTrackWarning,
    showDragDataError,
    showInvalidDragWarning,
    showWebAVNotReadyWarning,
    showMediaNotReadyWarning,

    // 确认对话框方法
    confirmDelete,
    confirmTrackDelete,
    confirmMediaDelete,
    confirmProjectSave,
    confirmBatchOperation,

    // 调试方法
    getDialogsSummary,
  }
}

/**
 * 兼容性导出 - 保持与旧版本的接口兼容
 */
export const useDialogs = useUnifiedDialogs
