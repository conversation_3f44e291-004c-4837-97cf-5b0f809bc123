/**
 * 统一拖拽预览管理器
 * 基于新架构的统一类型系统重构的拖拽预览功能
 * 
 * 架构特点：
 * 1. 使用 useUnifiedStore 替代旧的 videoStore
 * 2. 适配新的统一类型系统
 * 3. 增强的性能优化和错误处理
 * 4. 保持与旧版本兼容的接口
 * 5. 支持新架构的轨道和时间轴系统
 */

import { useUnifiedStore } from '../unifiedStore'
import type { DragPreviewData } from '../../types'

/**
 * 统一拖拽预览管理器类
 */
class UnifiedDragPreviewManager {
  private previewElement: HTMLElement | null = null
  private _unifiedStore: ReturnType<typeof useUnifiedStore> | null = null
  private updateTimer: number | null = null

  /**
   * 获取CSS变量值
   */
  private getCSSVariable(name: string): string {
    return getComputedStyle(document.documentElement).getPropertyValue(name).trim()
  }

  /**
   * 获取统一Store实例（延迟初始化）
   */
  private get unifiedStore() {
    if (!this._unifiedStore) {
      this._unifiedStore = useUnifiedStore()
    }
    return this._unifiedStore
  }

  /**
   * 显示拖拽预览
   */
  showPreview(data: DragPreviewData, timelineWidth: number) {
    this.hidePreview() // 先清理现有预览

    try {
      const preview = this.createPreviewElement(data)
      this.positionPreview(preview, data, timelineWidth)
      document.body.appendChild(preview)
      this.previewElement = preview

      console.log('🎨 [UnifiedDragPreview] 显示预览:', data)
    } catch (error) {
      console.error('❌ [UnifiedDragPreview] 显示预览失败:', error)
    }
  }

  /**
   * 更新预览位置和状态
   */
  updatePreview(data: DragPreviewData, timelineWidth: number) {
    // 取消之前的更新
    if (this.updateTimer) {
      cancelAnimationFrame(this.updateTimer)
    }

    if (this.previewElement) {
      // 使用 requestAnimationFrame 优化性能，确保每帧只更新一次
      this.updateTimer = requestAnimationFrame(() => {
        try {
          if (this.previewElement) {
            this.positionPreview(this.previewElement, data, timelineWidth)
            this.updatePreviewStyle(this.previewElement, data)
            this.updatePreviewContent(this.previewElement, data)
          }
        } catch (error) {
          console.error('❌ [UnifiedDragPreview] 更新预览失败:', error)
        }
        this.updateTimer = null
      })
    } else {
      this.showPreview(data, timelineWidth)
    }
  }

  /**
   * 隐藏预览
   */
  hidePreview() {
    // 取消待处理的更新
    if (this.updateTimer) {
      cancelAnimationFrame(this.updateTimer)
      this.updateTimer = null
    }

    if (this.previewElement) {
      this.previewElement.remove()
      this.previewElement = null
      console.log('🧹 [UnifiedDragPreview] 隐藏预览')
    }
  }

  /**
   * 创建预览元素
   */
  private createPreviewElement(data: DragPreviewData): HTMLElement {
    const preview = document.createElement('div')
    preview.className = 'unified-drag-preview'

    // 基础样式 - 使用高性能的CSS属性，高度与clip一致
    const backgroundColor = data.isConflict
      ? this.getCSSVariable('--color-drag-preview-conflict')
      : this.getCSSVariable('--color-drag-preview-normal')
    const borderColor = data.isConflict
      ? this.getCSSVariable('--color-drag-border-conflict')
      : this.getCSSVariable('--color-drag-border-normal')

    // 使用传入的高度，如果没有则使用默认值60px
    const previewHeight = data.height || 60

    preview.style.cssText = `
      position: fixed;
      left: 0;
      top: 0;
      height: ${previewHeight}px;
      background: ${backgroundColor};
      border: 2px solid ${borderColor};
      border-radius: 4px;
      pointer-events: none;
      z-index: 1001;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 11px;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      opacity: 0.9;
      will-change: transform;
    `

    // 设置内容
    this.updatePreviewContent(preview, data)

    return preview
  }

  /**
   * 更新预览内容
   */
  private updatePreviewContent(preview: HTMLElement, data: DragPreviewData) {
    if (data.isMultiple && data.count) {
      preview.textContent = `${data.count} 个项目`
    } else {
      const displayName = data.name.length > 12 ? data.name.substring(0, 10) + '..' : data.name
      preview.textContent = displayName
    }
  }

  /**
   * 定位预览元素（适配新架构）
   */
  private positionPreview(preview: HTMLElement, data: DragPreviewData, timelineWidth: number) {
    try {
      // 计算预览位置和尺寸（data.startTime 和 data.duration 已经是帧数）
      const startFrames = data.startTime
      const endFrames = data.startTime + data.duration

      // 使用工具函数计算像素每帧比例
      const pixelsPerFrame = (timelineWidth * this.unifiedStore.zoomLevel) / this.unifiedStore.totalDurationFrames
      const left = startFrames * pixelsPerFrame
      const right = endFrames * pixelsPerFrame
      const width = Math.max(right - left, 60) // 最小宽度60px

      // 获取目标轨道位置
      const trackSelector = `.track-content[data-track-id="${data.trackId}"]`
      const trackElement = document.querySelector(trackSelector) as HTMLElement

      if (trackElement) {
        const trackRect = trackElement.getBoundingClientRect()

        // 使用基础轨道高度，避免访问可能不存在的 layout 属性
        const trackHeight = 80 // 默认轨道高度
        const previewHeight = data.height || 60 // 预览高度

        // 计算垂直居中位置，与TimelineBaseClip的逻辑保持一致
        const topOffset = Math.max(5, (trackHeight - previewHeight) / 2) // 至少5px的上边距

        const finalLeft = trackRect.left + left
        const finalTop = trackRect.top + topOffset

        // 使用 transform 而不是 left/top 来提高性能
        preview.style.transform = `translate(${finalLeft}px, ${finalTop}px)`
        preview.style.width = `${width}px`

        // 只在第一次定位时设置 position
        if (!preview.style.position) {
          preview.style.position = 'fixed'
          preview.style.left = '0'
          preview.style.top = '0'
        }

        console.log('📍 [UnifiedDragPreview] 定位预览:', {
          startFrames,
          endFrames,
          left: finalLeft,
          top: finalTop,
          width,
          trackId: data.trackId
        })
      } else {
        console.warn('⚠️ [UnifiedDragPreview] 未找到目标轨道元素:', data.trackId)
      }
    } catch (error) {
      console.error('❌ [UnifiedDragPreview] 定位预览失败:', error)
    }
  }

  /**
   * 更新预览样式
   */
  private updatePreviewStyle(preview: HTMLElement, data: DragPreviewData) {
    // 更新冲突状态
    const borderColor = data.isConflict
      ? this.getCSSVariable('--color-drag-border-conflict')
      : this.getCSSVariable('--color-drag-border-normal')
    const backgroundColor = data.isConflict
      ? this.getCSSVariable('--color-drag-preview-conflict')
      : this.getCSSVariable('--color-drag-preview-normal')

    if (preview.style.borderColor !== borderColor) {
      preview.style.borderColor = borderColor
      preview.style.background = backgroundColor
    }
  }

  /**
   * 获取预览状态（用于调试）
   */
  getPreviewStatus() {
    return {
      hasPreview: !!this.previewElement,
      hasPendingUpdate: !!this.updateTimer,
      storeReady: !!this._unifiedStore,
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    this.hidePreview()
    this._unifiedStore = null
    console.log('🧹 [UnifiedDragPreview] 清理资源')
  }
}

// 全局实例变量
let _unifiedDragPreviewManager: UnifiedDragPreviewManager | null = null

/**
 * 获取统一拖拽预览管理器实例（延迟创建）
 */
export function getUnifiedDragPreviewManager(): UnifiedDragPreviewManager {
  if (!_unifiedDragPreviewManager) {
    _unifiedDragPreviewManager = new UnifiedDragPreviewManager()
    console.log('🏭 [UnifiedDragPreview] 创建管理器实例')
  }
  return _unifiedDragPreviewManager
}

/**
 * 重置管理器实例（用于测试或重新初始化）
 */
export function resetUnifiedDragPreviewManager() {
  if (_unifiedDragPreviewManager) {
    _unifiedDragPreviewManager.dispose()
    _unifiedDragPreviewManager = null
    console.log('🔄 [UnifiedDragPreview] 重置管理器实例')
  }
}

/**
 * 兼容性导出 - 保持与旧版本的接口兼容
 */
export const getDragPreviewManager = getUnifiedDragPreviewManager

// 导出类型定义
export type { DragPreviewData }
export { UnifiedDragPreviewManager }
