/**
 * 统一拖拽工具函数集合
 * 基于新架构的统一类型系统重构的拖拽处理逻辑
 * 
 * 架构特点：
 * 1. 使用 UnifiedTimelineItemData 和 UnifiedMediaItemData 类型
 * 2. 适配 useUnifiedStore 状态管理
 * 3. 保持与旧版本兼容的接口
 * 4. 增强的类型安全和错误处理
 * 5. 支持新架构的吸附和预览系统
 */

import { useUnifiedStore } from '../unifiedStore'
import type { 
  UnifiedTimelineItemData, 
  UnifiedMediaItemData,
  MediaType 
} from '../index'
import type { 
  TimelineItemDragData, 
  MediaItemDragData,
  DragPreviewData,
  TrackType 
} from '../../types'

// 导入新架构的工具函数
import { alignFramesToFrame } from '../../stores/utils/timeUtils'

/**
 * 统一拖拽工具函数集合
 * 提供统一的拖拽处理逻辑，避免代码重复
 */
export function useUnifiedDragUtils() {
  const unifiedStore = useUnifiedStore()

  /**
   * 设置时间轴项目拖拽数据（适配新架构）
   */
  function setTimelineItemDragData(
    event: DragEvent,
    itemId: string,
    trackId: string,
    startTime: number, // 帧数
    selectedItems: string[],
    dragOffset: { x: number; y: number },
  ) {
    const dragData: TimelineItemDragData = {
      type: 'timeline-item',
      itemId,
      trackId,
      startTime,
      selectedItems,
      dragOffset,
    }

    // 设置拖拽数据
    event.dataTransfer!.setData('application/timeline-item', JSON.stringify(dragData))
    event.dataTransfer!.effectAllowed = 'move'

    // 设置全局拖拽状态（用于dragover事件中访问）
    window.__timelineDragData = dragData

    console.log('🎯 [UnifiedDrag] 设置时间轴项目拖拽数据:', dragData)
    return dragData
  }

  /**
   * 设置媒体项目拖拽数据（适配新架构）
   */
  function setMediaItemDragData(
    event: DragEvent,
    mediaItemId: string,
    name: string,
    duration: number, // 帧数
    mediaType: MediaType,
  ) {
    const dragData: MediaItemDragData = {
      type: 'media-item',
      mediaItemId,
      name,
      duration,
      mediaType,
    }

    // 设置拖拽数据
    event.dataTransfer!.setData('application/media-item', JSON.stringify(dragData))
    event.dataTransfer!.effectAllowed = 'copy'

    // 设置全局拖拽状态
    window.__mediaDragData = dragData

    console.log('🎯 [UnifiedDrag] 设置媒体项目拖拽数据:', dragData)
    return dragData
  }

  /**
   * 获取当前时间轴项目拖拽数据
   */
  function getCurrentTimelineItemDragData(): TimelineItemDragData | null {
    return window.__timelineDragData || null
  }

  /**
   * 获取当前媒体项目拖拽数据
   */
  function getCurrentMediaItemDragData(): MediaItemDragData | null {
    return window.__mediaDragData || null
  }

  /**
   * 清理拖拽数据
   */
  function clearDragData() {
    window.__timelineDragData = undefined
    window.__mediaDragData = undefined
    console.log('🧹 [UnifiedDrag] 清理拖拽数据')
  }

  /**
   * 确保项目被选中（适配新架构的选择系统）
   */
  function ensureItemSelected(itemId: string) {
    if (!unifiedStore.isTimelineItemSelected(itemId)) {
      unifiedStore.selectTimelineItem(itemId)
      console.log('✅ [UnifiedDrag] 自动选中项目:', itemId)
    }
  }

  /**
   * 创建拖拽预览数据（适配新架构）
   */
  function createDragPreviewData(
    dragData: TimelineItemDragData | MediaItemDragData,
    startTime: number,
    trackId: string,
    isConflict: boolean = false,
    isMultiple: boolean = false,
    count?: number
  ): DragPreviewData {
    let name: string
    let mediaType: MediaType
    let duration: number

    if (dragData.type === 'timeline-item') {
      const timelineItem = unifiedStore.getTimelineItem(dragData.itemId)
      if (!timelineItem) {
        throw new Error(`时间轴项目不存在: ${dragData.itemId}`)
      }

      const mediaItem = unifiedStore.getMediaItem(timelineItem.mediaItemId)
      if (!mediaItem) {
        throw new Error(`关联的媒体项目不存在: ${timelineItem.mediaItemId}`)
      }

      name = timelineItem.config.name || mediaItem.name
      mediaType = timelineItem.mediaType as MediaType
      duration = timelineItem.timeRange.timelineEndTime - timelineItem.timeRange.timelineStartTime
    } else {
      const mediaItem = unifiedStore.getMediaItem(dragData.mediaItemId)
      if (!mediaItem) {
        throw new Error(`媒体项目不存在: ${dragData.mediaItemId}`)
      }

      name = dragData.name
      mediaType = dragData.mediaType as MediaType
      duration = dragData.duration
    }

    const previewData: DragPreviewData = {
      name,
      mediaType,
      duration,
      startTime,
      trackId,
      isConflict,
      isMultiple,
      count,
      height: getClipHeightByMediaType(mediaType),
    }

    console.log('🎨 [UnifiedDrag] 创建拖拽预览数据:', previewData)
    return previewData
  }

  /**
   * 根据媒体类型获取clip高度
   */
  function getClipHeightByMediaType(mediaType: MediaType): number {
    const heightMap: Record<MediaType, number> = {
      video: 80,
      audio: 60,
      image: 80,
      text: 60,
    }
    return heightMap[mediaType] || 60
  }

  /**
   * 获取被拖拽项目的高度
   */
  function getDraggedItemHeight(dragData: TimelineItemDragData | MediaItemDragData): number {
    if (dragData.type === 'timeline-item') {
      const timelineItem = unifiedStore.getTimelineItem(dragData.itemId)
      if (timelineItem) {
        return getClipHeightByMediaType(timelineItem.mediaType as MediaType)
      }
    } else {
      return getClipHeightByMediaType(dragData.mediaType as MediaType)
    }
    return 60 // 默认高度
  }

  /**
   * 计算拖拽放置位置（适配新架构）
   */
  function calculateDropPosition(
    event: DragEvent,
    timelineWidth: number,
    enableSnapping: boolean = true,
    excludeClipIds?: string[]
  ): { frame: number; snapResult?: any } {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const offsetX = event.clientX - rect.left

    // 计算基础帧数位置
    const pixelsPerFrame = (timelineWidth * unifiedStore.zoomLevel) / unifiedStore.totalDurationFrames
    let dropFrames = Math.round(offsetX / pixelsPerFrame)

    // 确保拖拽帧数不会小于0
    dropFrames = Math.max(0, dropFrames)

    // 对齐到帧边界
    dropFrames = alignFramesToFrame(dropFrames)

    // TODO: 集成新架构的吸附系统
    // 当新架构的吸附管理器准备好后，在这里添加吸附计算
    let snapResult
    if (enableSnapping) {
      // 暂时使用简单的帧对齐，等待新架构吸附系统
      console.log('🧲 [UnifiedDrag] 吸附功能待集成新架构系统')
    }

    console.log('📍 [UnifiedDrag] 计算放置位置:', { dropFrames, pixelsPerFrame })
    return { frame: dropFrames, snapResult }
  }

  /**
   * 查找最近的兼容轨道
   */
  function findNearestCompatibleTrack(
    mediaType: MediaType,
    targetY: number
  ): string | null {
    const tracks = unifiedStore.tracks
    let nearestTrack: string | null = null
    let minDistance = Infinity

    tracks.forEach(track => {
      if (isMediaCompatibleWithTrack(mediaType, track.type)) {
        // TODO: 计算轨道的实际Y位置
        // 这里需要根据实际的轨道布局来计算距离
        const trackY = 0 // 临时值，需要实际计算
        const distance = Math.abs(targetY - trackY)
        
        if (distance < minDistance) {
          minDistance = distance
          nearestTrack = track.id
        }
      }
    })

    console.log('🎯 [UnifiedDrag] 查找兼容轨道:', { mediaType, nearestTrack })
    return nearestTrack
  }

  /**
   * 检查媒体类型是否与轨道兼容
   */
  function isMediaCompatibleWithTrack(mediaType: MediaType, trackType: TrackType): boolean {
    const compatibilityMap: Record<MediaType, TrackType[]> = {
      video: ['video'],
      audio: ['audio'],
      image: ['video'], // 图片可以放在视频轨道
      text: ['text', 'subtitle'], // 文本可以放在文本或字幕轨道
    }

    const compatible = compatibilityMap[mediaType]?.includes(trackType) || false
    console.log('🔍 [UnifiedDrag] 检查兼容性:', { mediaType, trackType, compatible })
    return compatible
  }

  /**
   * 获取拖拽数据类型
   */
  function getDragDataType(event: DragEvent): 'timeline-item' | 'media-item' | null {
    const types = event.dataTransfer?.types || []
    if (types.includes('application/timeline-item')) {
      return 'timeline-item'
    }
    if (types.includes('application/media-item')) {
      return 'media-item'
    }
    return null
  }

  // DOM查询工具函数
  function getTimelineItemElement(itemId: string): HTMLElement | null {
    return document.querySelector(`[data-timeline-item-id="${itemId}"]`)
  }

  function getMediaItemElement(itemId: string): HTMLElement | null {
    return document.querySelector(`[data-media-item-id="${itemId}"]`)
  }

  function getTrackElement(trackId: string): HTMLElement | null {
    return document.querySelector(`[data-track-id="${trackId}"]`)
  }

  function getElementDimensions(element: HTMLElement | null) {
    if (!element) return { width: 0, height: 0, x: 0, y: 0 }
    const rect = element.getBoundingClientRect()
    return {
      width: rect.width,
      height: rect.height,
      x: rect.left,
      y: rect.top,
    }
  }

  return {
    // 拖拽数据管理
    setTimelineItemDragData,
    setMediaItemDragData,
    getCurrentTimelineItemDragData,
    getCurrentMediaItemDragData,
    clearDragData,

    // 选择管理
    ensureItemSelected,

    // 预览数据创建
    createDragPreviewData,
    getClipHeightByMediaType,
    getDraggedItemHeight,

    // 位置计算
    calculateDropPosition,
    findNearestCompatibleTrack,
    isMediaCompatibleWithTrack,

    // 类型检查
    getDragDataType,

    // DOM查询工具
    getTimelineItemElement,
    getMediaItemElement,
    getTrackElement,
    getElementDimensions,
  }
}

/**
 * 兼容性导出 - 保持与旧版本的接口兼容
 */
export const useDragUtils = useUnifiedDragUtils
