/**
 * 统一键盘快捷键处理
 * 基于新架构的统一类型系统重构的键盘快捷键功能
 * 
 * 架构特点：
 * 1. 使用 useUnifiedStore 替代旧的 videoStore
 * 2. 适配新的统一历史记录系统
 * 3. 支持新架构的播放控制
 * 4. 增强的快捷键管理和冲突检测
 * 5. 保持与旧版本兼容的接口
 */

import { onMounted, onUnmounted } from 'vue'
import { useUnifiedStore } from '../unifiedStore'

/**
 * 统一键盘快捷键处理组合式函数
 * 提供全局快捷键支持，包括撤销/重做等操作
 */
export function useUnifiedKeyboardShortcuts() {
  const unifiedStore = useUnifiedStore()

  /**
   * 检查元素是否为输入元素
   * @param element HTML元素
   * @returns 是否为输入元素
   */
  function isInputElement(element: HTMLElement): boolean {
    // TEXTAREA 和可编辑元素：总是阻止快捷键（用户正在输入文本）
    if (element.tagName === 'TEXTAREA' || element.isContentEditable) {
      return true
    }

    // INPUT 元素：根据类型判断
    if (element.tagName === 'INPUT') {
      const inputElement = element as HTMLInputElement
      const inputType = inputElement.type.toLowerCase()

      // 只有滑块类型允许快捷键，其他输入类型都阻止
      // range 滑块：允许快捷键（实时更新，用户希望能快速撤销调整）
      return inputType !== 'range'
    }

    return false
  }

  /**
   * 处理撤销操作
   */
  async function handleUndo() {
    try {
      console.log('🎹 [UnifiedShortcuts] 快捷键触发: 撤销 (Ctrl+Z)')
      await unifiedStore.undo()
    } catch (error) {
      console.error('❌ [UnifiedShortcuts] 撤销操作失败:', error)
    }
  }

  /**
   * 处理重做操作
   */
  async function handleRedo() {
    try {
      console.log('🎹 [UnifiedShortcuts] 快捷键触发: 重做 (Ctrl+Y)')
      await unifiedStore.redo()
    } catch (error) {
      console.error('❌ [UnifiedShortcuts] 重做操作失败:', error)
    }
  }

  // 注意：为了保持与旧版本的逻辑一致性，只实现撤销/重做功能
  // 旧版本中没有其他快捷键功能，应该在后续版本中逐步添加

  /**
   * 处理键盘事件（保持与旧版本一致，只处理撤销/重做）
   * @param event 键盘事件
   */
  const handleKeyDown = async (event: KeyboardEvent) => {
    // 检查是否在输入框中，如果是则不处理快捷键
    const target = event.target as HTMLElement

    if (isInputElement(target)) {
      return
    }

    // 检查修饰键组合
    const isCtrl = event.ctrlKey || event.metaKey // 支持 Mac 的 Cmd 键
    const isShift = event.shiftKey
    const isAlt = event.altKey

    // 撤销/重做快捷键（与旧版本保持一致）
    if (isCtrl && !isShift && !isAlt) {
      switch (event.key.toLowerCase()) {
        case 'z':
          event.preventDefault()
          console.log('🎹 [UnifiedShortcuts] 快捷键触发: 撤销 (Ctrl+Z)')
          await handleUndo()
          break

        case 'y':
          event.preventDefault()
          console.log('🎹 [UnifiedShortcuts] 快捷键触发: 重做 (Ctrl+Y)')
          await handleRedo()
          break
      }
    }

    // Ctrl+Shift+Z 也可以触发重做（常见的替代快捷键）
    if (isCtrl && isShift && !isAlt && event.key.toLowerCase() === 'z') {
      event.preventDefault()
      console.log('🎹 [UnifiedShortcuts] 快捷键触发: 重做 (Ctrl+Shift+Z)')
      await handleRedo()
    }
  }

  /**
   * 注册快捷键监听器
   */
  const registerShortcuts = () => {
    document.addEventListener('keydown', handleKeyDown)
    console.log('✅ [UnifiedShortcuts] 快捷键监听器已注册')
  }

  /**
   * 注销快捷键监听器
   */
  const unregisterShortcuts = () => {
    document.removeEventListener('keydown', handleKeyDown)
    console.log('🗑️ [UnifiedShortcuts] 快捷键监听器已注销')
  }

  /**
   * 获取快捷键状态摘要（用于调试）
   */
  function getShortcutsSummary() {
    return {
      hasUndo: unifiedStore.canUndo,
      hasRedo: unifiedStore.canRedo,
      selectedItemsCount: unifiedStore.selectedTimelineItemIds.size,
      hasCurrentProject: unifiedStore.hasCurrentProject,
      isPlaying: unifiedStore.isPlaying,
    }
  }

  // 在组件挂载时注册，卸载时注销
  onMounted(() => {
    registerShortcuts()
  })

  onUnmounted(() => {
    unregisterShortcuts()
  })

  return {
    // 注册/注销方法（与旧版本保持一致）
    registerShortcuts,
    unregisterShortcuts,

    // 手动触发方法（只保留旧版本存在的功能）
    handleUndo,
    handleRedo,

    // 调试方法
    getShortcutsSummary,
  }
}

/**
 * 兼容性导出 - 保持与旧版本的接口兼容
 */
export const useKeyboardShortcuts = useUnifiedKeyboardShortcuts
