/**
 * 统一播放控制 Composable
 * 基于新架构的统一类型系统重构的播放控制工具函数
 * 
 * 架构特点：
 * 1. 使用 useUnifiedStore 替代旧的 videoStore
 * 2. 适配新的统一状态管理系统
 * 3. 提供与旧版本兼容的接口
 * 4. 增强的错误处理和日志记录
 * 5. 支持新架构的WebAV集成方式
 */

import { useUnifiedStore } from '../unifiedStore'

/**
 * 统一播放控制工具函数
 * 提供可复用的播放控制逻辑，避免代码重复
 */
export function useUnifiedPlaybackControls() {
  const unifiedStore = useUnifiedStore()

  /**
   * 安全地暂停播放
   * 在需要暂停播放进行编辑操作时调用
   * @param reason 暂停原因，用于调试日志
   */
  function pauseForEditing(reason: string = '编辑操作') {
    if (unifiedStore.isWebAVReadyGlobal && unifiedStore.isPlaying) {
      console.log(`⏸️ [UnifiedPlayback] 因${reason}暂停播放`)
      unifiedStore.pause()
      return true // 返回是否实际执行了暂停
    }
    return false
  }

  /**
   * 检查WebAV是否就绪，如果未就绪则显示警告
   * @param operation 操作名称，用于错误日志
   * @returns 是否就绪
   */
  function ensureWebAVReady(operation: string = '操作'): boolean {
    if (!unifiedStore.isWebAVReadyGlobal) {
      console.warn(`⚠️ [UnifiedPlayback] WebAV canvas not ready for ${operation}`)
      return false
    }
    return true
  }

  /**
   * 安全地执行播放控制操作
   * @param operation 要执行的操作函数
   * @param operationName 操作名称，用于日志
   * @returns 操作是否成功执行
   */
  function safePlaybackOperation(operation: () => void, operationName: string): boolean {
    if (!ensureWebAVReady(operationName)) {
      return false
    }

    try {
      operation()
      console.log(`✅ [UnifiedPlayback] ${operationName}执行成功`)
      return true
    } catch (error) {
      console.error(`❌ [UnifiedPlayback] ${operationName}执行失败:`, error)
      return false
    }
  }

  /**
   * 重启播放（用于播放速度变更等场景）
   * @param delay 重启延迟时间（毫秒）
   */
  function restartPlayback(delay: number = 50) {
    if (!ensureWebAVReady('重启播放')) return

    const wasPlaying = unifiedStore.isPlaying
    if (wasPlaying) {
      console.log('🔄 [UnifiedPlayback] 重启播放以应用新设置')
      unifiedStore.pause()
      setTimeout(() => {
        if (unifiedStore.isWebAVReadyGlobal) {
          unifiedStore.play()
        }
      }, delay)
    }
  }

  /**
   * 安全地跳转到指定帧
   * @param frame 目标帧数
   * @param reason 跳转原因，用于日志
   */
  function safeSeekToFrame(frame: number, reason: string = '用户操作') {
    return safePlaybackOperation(() => {
      unifiedStore.seekToFrame(frame)
      console.log(`🎯 [UnifiedPlayback] 跳转到帧 ${frame} (${reason})`)
    }, `跳转到帧${frame}`)
  }

  /**
   * 安全地设置播放速度
   * @param rate 播放速度倍率
   * @param restart 是否重启播放以应用新速度
   */
  function safeSetPlaybackRate(rate: number, restart: boolean = true) {
    return safePlaybackOperation(() => {
      unifiedStore.setPlaybackRate(rate)
      if (restart) {
        restartPlayback()
      }
      console.log(`⚡ [UnifiedPlayback] 设置播放速度为 ${rate}x`)
    }, `设置播放速度${rate}x`)
  }

  /**
   * 切换播放/暂停状态
   * @param reason 操作原因，用于日志
   */
  function togglePlayPause(reason: string = '用户操作') {
    return safePlaybackOperation(() => {
      if (unifiedStore.isPlaying) {
        unifiedStore.pause()
        console.log(`⏸️ [UnifiedPlayback] 暂停播放 (${reason})`)
      } else {
        unifiedStore.play()
        console.log(`▶️ [UnifiedPlayback] 开始播放 (${reason})`)
      }
    }, '播放/暂停切换')
  }

  /**
   * 停止播放并重置到开始位置
   * @param reason 操作原因，用于日志
   */
  function stopAndReset(reason: string = '用户操作') {
    return safePlaybackOperation(() => {
      unifiedStore.stop()
      unifiedStore.seekToFrame(0)
      console.log(`⏹️ [UnifiedPlayback] 停止播放并重置 (${reason})`)
    }, '停止播放')
  }

  /**
   * 帧级别导航 - 下一帧
   */
  function nextFrame() {
    return safePlaybackOperation(() => {
      unifiedStore.nextFrame()
    }, '下一帧')
  }

  /**
   * 帧级别导航 - 上一帧
   */
  function previousFrame() {
    return safePlaybackOperation(() => {
      unifiedStore.previousFrame()
    }, '上一帧')
  }

  /**
   * 按帧数偏移跳转
   * @param offset 帧数偏移量（正数向前，负数向后）
   */
  function seekByFrames(offset: number) {
    return safePlaybackOperation(() => {
      unifiedStore.seekByFrames(offset)
      console.log(`🎯 [UnifiedPlayback] 偏移 ${offset} 帧`)
    }, `偏移${offset}帧`)
  }

  /**
   * 获取播放状态摘要（用于调试）
   */
  function getPlaybackSummary() {
    return {
      isPlaying: unifiedStore.isPlaying,
      currentFrame: unifiedStore.currentFrame,
      playbackRate: unifiedStore.playbackRate,
      isWebAVReady: unifiedStore.isWebAVReadyGlobal,
      formattedTime: unifiedStore.formattedCurrentTime,
    }
  }

  return {
    // 基础播放控制
    pauseForEditing,
    ensureWebAVReady,
    safePlaybackOperation,
    restartPlayback,
    
    // 播放状态控制
    togglePlayPause,
    stopAndReset,
    
    // 帧级别控制
    safeSeekToFrame,
    nextFrame,
    previousFrame,
    seekByFrames,
    
    // 播放速度控制
    safeSetPlaybackRate,
    
    // 调试和状态
    getPlaybackSummary,
  }
}

/**
 * 兼容性导出 - 保持与旧版本的接口兼容
 */
export const usePlaybackControls = useUnifiedPlaybackControls
