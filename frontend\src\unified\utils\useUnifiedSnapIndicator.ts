/**
 * 统一吸附指示器管理器
 * 基于新架构的统一类型系统重构的吸附指示器功能
 * 
 * 架构特点：
 * 1. 适配新的统一类型系统
 * 2. 增强的性能优化和状态管理
 * 3. 保持与旧版本兼容的接口
 * 4. 支持新架构的响应式系统
 * 5. 改进的错误处理和调试功能
 */

import { ref, reactive } from 'vue'
import type { SnapPoint } from '../../types/snap'
import type { SnapIndicatorData } from '../../types'

/**
 * 统一吸附指示器管理器类
 * 统一管理吸附指示器的显示、隐藏和更新
 */
class UnifiedSnapIndicatorManager {
  private indicatorData = reactive<SnapIndicatorData>({
    show: false,
    timelineWidth: 0,
    timelineOffset: { x: 0, y: 0 },
    showTooltip: true,
    lineHeight: 400
  })

  private isVisible = ref(false)
  private hideTimeout: number | null = null
  private updateCount = ref(0)

  /**
   * 获取当前指示器数据（响应式）
   */
  get data() {
    return this.indicatorData
  }

  /**
   * 获取指示器可见状态
   */
  get visible() {
    return this.isVisible.value
  }

  /**
   * 获取更新计数（用于调试）
   */
  get updates() {
    return this.updateCount.value
  }

  /**
   * 显示吸附指示器
   */
  show(snapPoint: SnapPoint, timelineWidth: number, options: Partial<SnapIndicatorData> = {}) {
    try {
      // 清除之前的隐藏定时器
      if (this.hideTimeout) {
        clearTimeout(this.hideTimeout)
        this.hideTimeout = null
      }

      // 更新指示器数据
      Object.assign(this.indicatorData, {
        show: true,
        snapPoint,
        timelineWidth,
        ...options
      })

      this.isVisible.value = true
      this.updateCount.value++

      console.log('🧲 [UnifiedSnapIndicator] 显示指示器:', {
        吸附点类型: snapPoint.type,
        帧位置: snapPoint.frame,
        时间轴宽度: timelineWidth,
        更新次数: this.updateCount.value
      })

    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 显示指示器失败:', error)
    }
  }

  /**
   * 隐藏吸附指示器
   */
  hide(immediate: boolean = false) {
    try {
      if (immediate) {
        this._hideImmediate()
      } else {
        // 延迟隐藏，避免频繁闪烁
        if (this.hideTimeout) {
          clearTimeout(this.hideTimeout)
        }
        
        this.hideTimeout = window.setTimeout(() => {
          this._hideImmediate()
          this.hideTimeout = null
        }, 100)
      }
    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 隐藏指示器失败:', error)
    }
  }

  /**
   * 立即隐藏指示器
   */
  private _hideImmediate() {
    this.indicatorData.show = false
    this.isVisible.value = false
    this.indicatorData.snapPoint = undefined
    
    console.log('🫥 [UnifiedSnapIndicator] 隐藏指示器')
  }

  /**
   * 更新指示器位置
   */
  updatePosition(snapPoint: SnapPoint, timelineWidth: number) {
    try {
      if (this.isVisible.value) {
        this.indicatorData.snapPoint = snapPoint
        this.indicatorData.timelineWidth = timelineWidth
        this.updateCount.value++

        console.log('📍 [UnifiedSnapIndicator] 更新位置:', {
          吸附点类型: snapPoint.type,
          帧位置: snapPoint.frame,
          时间轴宽度: timelineWidth
        })
      }
    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 更新位置失败:', error)
    }
  }

  /**
   * 更新时间轴偏移量
   */
  updateTimelineOffset(offset: { x: number; y: number }) {
    try {
      this.indicatorData.timelineOffset = { ...offset }
      
      console.log('📐 [UnifiedSnapIndicator] 更新时间轴偏移:', offset)
    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 更新时间轴偏移失败:', error)
    }
  }

  /**
   * 更新指示线高度
   */
  updateLineHeight(height: number) {
    try {
      if (height > 0) {
        this.indicatorData.lineHeight = height
        
        console.log('📏 [UnifiedSnapIndicator] 更新指示线高度:', height)
      } else {
        console.warn('⚠️ [UnifiedSnapIndicator] 无效的指示线高度:', height)
      }
    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 更新指示线高度失败:', error)
    }
  }

  /**
   * 设置工具提示显示状态
   */
  setTooltipVisible(visible: boolean) {
    try {
      this.indicatorData.showTooltip = visible
      
      console.log('💬 [UnifiedSnapIndicator] 设置工具提示可见性:', visible)
    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 设置工具提示可见性失败:', error)
    }
  }

  /**
   * 批量更新指示器配置
   */
  updateConfig(config: Partial<SnapIndicatorData>) {
    try {
      Object.assign(this.indicatorData, config)
      this.updateCount.value++
      
      console.log('⚙️ [UnifiedSnapIndicator] 批量更新配置:', config)
    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 批量更新配置失败:', error)
    }
  }

  /**
   * 重置指示器到默认状态
   */
  reset() {
    try {
      this.hide(true)
      
      Object.assign(this.indicatorData, {
        show: false,
        timelineWidth: 0,
        timelineOffset: { x: 0, y: 0 },
        showTooltip: true,
        lineHeight: 400,
        snapPoint: undefined
      })
      
      this.updateCount.value = 0
      
      console.log('🔄 [UnifiedSnapIndicator] 重置指示器状态')
    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 重置指示器失败:', error)
    }
  }

  /**
   * 获取指示器状态摘要（用于调试）
   */
  getStatusSummary() {
    return {
      可见状态: this.isVisible.value,
      更新次数: this.updateCount.value,
      有隐藏定时器: !!this.hideTimeout,
      当前配置: {
        显示: this.indicatorData.show,
        时间轴宽度: this.indicatorData.timelineWidth,
        指示线高度: this.indicatorData.lineHeight,
        显示工具提示: this.indicatorData.showTooltip,
        时间轴偏移: this.indicatorData.timelineOffset
      },
      吸附点信息: this.indicatorData.snapPoint ? {
        类型: this.indicatorData.snapPoint.type,
        帧位置: this.indicatorData.snapPoint.frame,
        项目ID: this.indicatorData.snapPoint.itemId,
        项目名称: this.indicatorData.snapPoint.itemName
      } : null
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    try {
      if (this.hideTimeout) {
        clearTimeout(this.hideTimeout)
        this.hideTimeout = null
      }
      
      this._hideImmediate()
      this.updateCount.value = 0
      
      console.log('🧹 [UnifiedSnapIndicator] 清理资源')
    } catch (error) {
      console.error('❌ [UnifiedSnapIndicator] 清理资源失败:', error)
    }
  }
}

// 全局实例变量
let _unifiedSnapIndicatorManager: UnifiedSnapIndicatorManager | null = null

/**
 * 获取统一吸附指示器管理器实例（延迟创建）
 */
export function getUnifiedSnapIndicatorManager(): UnifiedSnapIndicatorManager {
  if (!_unifiedSnapIndicatorManager) {
    _unifiedSnapIndicatorManager = new UnifiedSnapIndicatorManager()
    console.log('🏭 [UnifiedSnapIndicator] 创建管理器实例')
  }
  return _unifiedSnapIndicatorManager
}

/**
 * 重置管理器实例（用于测试或重新初始化）
 */
export function resetUnifiedSnapIndicatorManager() {
  if (_unifiedSnapIndicatorManager) {
    _unifiedSnapIndicatorManager.dispose()
    _unifiedSnapIndicatorManager = null
    console.log('🔄 [UnifiedSnapIndicator] 重置管理器实例')
  }
}

/**
 * 统一吸附指示器管理器 Composable
 * 提供响应式的吸附指示器管理功能
 */
export function useUnifiedSnapIndicator() {
  const manager = getUnifiedSnapIndicatorManager()

  return {
    // 响应式数据
    indicatorData: manager.data,
    isVisible: manager.visible,
    updateCount: manager.updates,

    // 管理方法
    show: manager.show.bind(manager),
    hide: manager.hide.bind(manager),
    updatePosition: manager.updatePosition.bind(manager),
    updateTimelineOffset: manager.updateTimelineOffset.bind(manager),
    updateLineHeight: manager.updateLineHeight.bind(manager),
    setTooltipVisible: manager.setTooltipVisible.bind(manager),
    updateConfig: manager.updateConfig.bind(manager),
    reset: manager.reset.bind(manager),
    dispose: manager.dispose.bind(manager),

    // 调试方法
    getStatusSummary: manager.getStatusSummary.bind(manager)
  }
}

/**
 * 兼容性导出 - 保持与旧版本的接口兼容
 */
export const useSnapIndicator = useUnifiedSnapIndicator
export const getSnapIndicatorManager = getUnifiedSnapIndicatorManager

// 导出类型和管理器类
export { UnifiedSnapIndicatorManager }
