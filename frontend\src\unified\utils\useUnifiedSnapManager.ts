/**
 * 统一吸附管理器
 * 基于新架构的统一类型系统重构的吸附管理功能
 * 
 * 架构特点：
 * 1. 使用 useUnifiedStore 替代旧的数据源
 * 2. 适配新的统一时间轴项目类型
 * 3. 支持新架构的轨道系统
 * 4. 增强的性能优化和缓存机制
 * 5. 保持与旧版本兼容的接口
 */

import { ref } from 'vue'
import { useUnifiedStore } from '../unifiedStore'
import { useSnapConfig } from '../../composables/useSnapConfig'
import { SnapCalculator } from '../../utils/snapCalculator'
import type { 
  SnapResult, 
  SnapCalculationOptions, 
  SnapPointCollectionOptions 
} from '../../types/snap'

/**
 * 统一吸附管理器 Composable
 * 统一管理所有吸附逻辑，提供高级吸附计算接口
 */
export function useUnifiedSnapManager() {
  const unifiedStore = useUnifiedStore()
  const snapConfig = useSnapConfig()
  const calculator = new SnapCalculator()
  
  // 缓存相关
  const snapPointsCache = ref<Map<string, any>>(new Map())
  const lastCacheKey = ref<string>('')
  
  /**
   * 生成缓存键
   */
  function generateCacheKey(options: SnapPointCollectionOptions): string {
    return JSON.stringify({
      includeClipBoundaries: options.includeClipBoundaries,
      includeKeyframes: options.includeKeyframes,
      includePlayhead: options.includePlayhead,
      includeTimelineStart: options.includeTimelineStart,
      excludeClipIds: options.excludeClipIds?.sort(),
      frameRange: options.frameRange,
      // 添加数据版本标识，确保数据变化时缓存失效
      dataVersion: getDataVersion()
    })
  }

  /**
   * 获取数据版本标识（用于缓存失效）
   */
  function getDataVersion(): string {
    return `${unifiedStore.timelineItems.length}-${unifiedStore.currentFrame}-${Date.now()}`
  }
  
  /**
   * 清除吸附点缓存
   */
  function clearSnapPointsCache() {
    snapPointsCache.value.clear()
    lastCacheKey.value = ''
    console.log('🧹 [UnifiedSnapManager] 清除吸附点缓存')
  }

  /**
   * 收集吸附点（适配新架构）
   */
  function collectUnifiedSnapPoints(options: SnapPointCollectionOptions) {
    const snapPoints: any[] = []

    try {
      // 1. 收集片段边界吸附点
      if (options.includeClipBoundaries) {
        unifiedStore.timelineItems.forEach(item => {
          // 排除指定的片段
          if (options.excludeClipIds?.includes(item.id)) {
            return
          }

          // 添加开始和结束位置
          snapPoints.push({
            frame: item.timeRange.timelineStartTime,
            type: 'clip-start',
            itemId: item.id,
            itemName: item.config.name || '未命名'
          })

          snapPoints.push({
            frame: item.timeRange.timelineEndTime,
            type: 'clip-end',
            itemId: item.id,
            itemName: item.config.name || '未命名'
          })
        })
      }

      // 2. 收集关键帧吸附点
      if (options.includeKeyframes) {
        unifiedStore.timelineItems.forEach(item => {
          if (options.excludeClipIds?.includes(item.id)) {
            return
          }

          // TODO: 适配新架构的关键帧系统
          // 这里需要根据新架构的关键帧数据结构来实现
          console.log('🔧 [UnifiedSnapManager] 关键帧吸附点收集待实现')
        })
      }

      // 3. 收集播放头吸附点
      if (options.includePlayhead) {
        snapPoints.push({
          frame: unifiedStore.currentFrame,
          type: 'playhead',
          itemId: 'playhead',
          itemName: '播放头'
        })
      }

      // 4. 收集时间轴起始位置吸附点
      if (options.includeTimelineStart) {
        snapPoints.push({
          frame: 0,
          type: 'timeline-start',
          itemId: 'timeline-start',
          itemName: '时间轴起始'
        })
      }

      console.log('📍 [UnifiedSnapManager] 收集吸附点:', {
        总数: snapPoints.length,
        片段边界: snapPoints.filter(p => p.type.includes('clip')).length,
        关键帧: snapPoints.filter(p => p.type === 'keyframe').length,
        播放头: snapPoints.filter(p => p.type === 'playhead').length,
        时间轴起始: snapPoints.filter(p => p.type === 'timeline-start').length
      })

      return snapPoints

    } catch (error) {
      console.error('❌ [UnifiedSnapManager] 收集吸附点失败:', error)
      return []
    }
  }
  
  /**
   * 计算吸附结果（主要接口）
   */
  function calculateSnap(
    targetFrame: number,
    timelineWidth: number,
    options: SnapCalculationOptions = {}
  ): SnapResult {
    // 检查是否启用吸附
    if (!snapConfig.isActuallyEnabled.value || options.temporaryDisabled) {
      return {
        snapped: false,
        frame: targetFrame
      }
    }
    
    try {
      // 构建吸附点收集选项
      const collectionOptions: SnapPointCollectionOptions = {
        includeClipBoundaries: snapConfig.isClipBoundariesEnabled.value,
        includeKeyframes: snapConfig.isKeyframesEnabled.value,
        includePlayhead: snapConfig.isPlayheadEnabled.value,
        includeTimelineStart: snapConfig.isTimelineStartEnabled.value,
        excludeClipIds: options.excludeClipIds
      }
      
      // 收集吸附点（使用缓存，与旧版本保持一致）
      const cacheKey = generateCacheKey(collectionOptions)
      let snapPoints

      if (lastCacheKey.value === cacheKey && snapPointsCache.value.has(cacheKey)) {
        snapPoints = snapPointsCache.value.get(cacheKey)
        console.log('📋 [UnifiedSnapManager] 使用缓存的吸附点')
      } else {
        // 使用统一的吸附点收集方法
        snapPoints = collectUnifiedSnapPoints(collectionOptions)
        snapPointsCache.value.set(cacheKey, snapPoints)
        lastCacheKey.value = cacheKey
        console.log('🔄 [UnifiedSnapManager] 重新收集吸附点')
      }

      // 计算阈值（帧数），使用工具函数计算
      const pixelThreshold = options.customThreshold ?? snapConfig.config.value.threshold
      const pixelsPerFrame = (timelineWidth * unifiedStore.zoomLevel) / unifiedStore.totalDurationFrames
      const thresholdFrames = pixelThreshold / pixelsPerFrame
      
      // 执行吸附计算
      const result = calculator.calculateSnap(targetFrame, snapPoints, thresholdFrames)
      
      if (result.snapped) {
        console.log('🧲 [UnifiedSnapManager] 吸附成功:', {
          原始帧: targetFrame,
          吸附帧: result.frame,
          吸附点类型: result.snapPoint?.type,
          阈值: thresholdFrames
        })
      }
      
      return result

    } catch (error) {
      console.error('❌ [UnifiedSnapManager] 吸附计算失败:', error)
      return {
        snapped: false,
        frame: targetFrame
      }
    }
  }
  
  /**
   * 专门用于播放头吸附的方法
   */
  function calculatePlayheadSnap(
    targetFrame: number,
    timelineWidth: number,
    options: Omit<SnapCalculationOptions, 'excludeClipIds'> = {}
  ): SnapResult {
    return calculateSnap(targetFrame, timelineWidth, {
      ...options,
      excludeClipIds: [] // 播放头不需要排除任何片段
    })
  }
  
  /**
   * 专门用于片段拖拽吸附的方法
   */
  function calculateClipDragSnap(
    targetFrame: number,
    timelineWidth: number,
    draggedClipIds: string[],
    options: Omit<SnapCalculationOptions, 'excludeClipIds'> = {}
  ): SnapResult {
    return calculateSnap(targetFrame, timelineWidth, {
      ...options,
      excludeClipIds: draggedClipIds // 排除正在拖拽的片段
    })
  }
  
  /**
   * 专门用于片段调整大小吸附的方法
   */
  function calculateClipResizeSnap(
    targetFrame: number,
    timelineWidth: number,
    resizingClipId: string,
    options: Omit<SnapCalculationOptions, 'excludeClipIds'> = {}
  ): SnapResult {
    return calculateSnap(targetFrame, timelineWidth, {
      ...options,
      excludeClipIds: [resizingClipId] // 排除正在调整大小的片段
    })
  }
  
  /**
   * 批量计算多个位置的吸附结果
   */
  function calculateMultipleSnaps(
    targetFrames: number[],
    timelineWidth: number,
    options: SnapCalculationOptions = {}
  ): SnapResult[] {
    return targetFrames.map(frame => 
      calculateSnap(frame, timelineWidth, options)
    )
  }

  /**
   * 检查指定帧是否接近吸附点
   */
  function isNearSnapPoint(
    frame: number,
    timelineWidth: number,
    threshold?: number
  ): boolean {
    const result = calculateSnap(frame, timelineWidth, { 
      customThreshold: threshold 
    })
    return result.snapped
  }

  /**
   * 获取指定范围内的吸附点
   */
  function getSnapPointsInRange(
    startFrame: number,
    endFrame: number,
    options: SnapPointCollectionOptions = {}
  ) {
    const allSnapPoints = collectUnifiedSnapPoints({
      includeClipBoundaries: true,
      includeKeyframes: true,
      includePlayhead: true,
      includeTimelineStart: true,
      ...options
    })

    return allSnapPoints.filter(point => 
      point.frame >= startFrame && point.frame <= endFrame
    )
  }

  /**
   * 获取吸附管理器状态摘要（用于调试）
   */
  function getSnapManagerSummary() {
    return {
      配置: {
        启用: snapConfig.isActuallyEnabled.value,
        片段边界: snapConfig.isClipBoundariesEnabled.value,
        关键帧: snapConfig.isKeyframesEnabled.value,
        播放头: snapConfig.isPlayheadEnabled.value,
        时间轴起始: snapConfig.isTimelineStartEnabled.value,
        阈值: snapConfig.config.value.threshold
      },
      缓存: {
        缓存项数: snapPointsCache.value.size,
        最后缓存键: lastCacheKey.value
      },
      数据: {
        时间轴项目数: unifiedStore.timelineItems.length,
        当前帧: unifiedStore.currentFrame
      }
    }
  }
  
  return {
    // 配置管理
    snapConfig,
    
    // 主要计算接口
    calculateSnap,
    calculatePlayheadSnap,
    calculateClipDragSnap,
    calculateClipResizeSnap,
    calculateMultipleSnaps,
    
    // 辅助方法
    isNearSnapPoint,
    getSnapPointsInRange,
    
    // 缓存管理
    clearSnapPointsCache,
    
    // 调试方法
    getSnapManagerSummary,
    
    // 状态
    isSnapEnabled: snapConfig.isActuallyEnabled
  }
}

/**
 * 兼容性导出 - 保持与旧版本的接口兼容
 */
export const useSnapManager = useUnifiedSnapManager
